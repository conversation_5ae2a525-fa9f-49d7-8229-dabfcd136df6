/**
 * Chat Utilities
 * Helper functions for chat functionality
 */

import { FILENAME_MAPPING, MESSAGE_TYPES, CHAT_MESSAGES } from "./constants/chat.js";

/**
 * Normalize month name to match FILENAME_MAPPING keys
 * Handles case variations: "june", "Jun", "JUNE" -> "June"
 * Handles abbreviations: "jun" -> "June", "jul" -> "July", "aug" -> "August"
 * @param {string} month - Month name in various formats
 * @returns {string} Normalized month name (June, July, or August)
 */
const normalizeMonthName = (month) => {
  if (!month || typeof month !== 'string') return month;
  
  const monthLower = month.toLowerCase();
  
  // Map valid months and their abbreviations to standardized names
  // Only months that exist in actual files: June, July, August
  const monthMap = {
    'june': 'June',
    'jun': 'June',
    'july': 'July',
    'jul': 'July',
    'august': 'August',
    'aug': 'August',
  };
  
  // If exact match found, return normalized version
  if (monthMap[monthLower]) {
    return monthMap[monthLower];
  }
  
  // Otherwise, try to capitalize first letter and lowercase rest
  // This handles cases like "June", "JUNE", etc.
  return month.charAt(0).toUpperCase() + month.slice(1).toLowerCase();
};

/**
 * Generate PDF filename based on dashboard configuration
 * @param {string} selectedMonth - Selected month (June, July, August, or variations)
 * @param {boolean} isFinancialSelected - Financial dashboard selected
 * @param {boolean} isOperationsSelected - Operations dashboard selected
 * @param {boolean} isPayrollSelected - Payroll dashboard selected
 * @param {string} selectedDashboard - Dashboard type (chp/dental)
 * @returns {string} Generated filename matching actual files in /uploads directories
 */
export const generatePdfFilename = (
  selectedMonth,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected,
  selectedDashboard = 'chp'
) => {
  // Normalize month name to match mapping keys (June, July, August)
  const normalizedMonth = normalizeMonthName(selectedMonth);
  const dashboard = selectedDashboard.toLowerCase();
  
  if (dashboard === 'chp') {
    if (isFinancialSelected) {
      return FILENAME_MAPPING.CHP.FINANCIAL[normalizedMonth] || FILENAME_MAPPING.DEFAULT;
    } else if (isOperationsSelected) {
      return FILENAME_MAPPING.CHP.OPERATIONS[normalizedMonth] || FILENAME_MAPPING.DEFAULT;
    } else if (isPayrollSelected) {
      return FILENAME_MAPPING.CHP.PAYROLL[normalizedMonth] || FILENAME_MAPPING.DEFAULT;
    }
  } else if (dashboard === 'dental') {
    if (isFinancialSelected) {
      return FILENAME_MAPPING.DENTAL.FINANCIAL[normalizedMonth] || FILENAME_MAPPING.DEFAULT;
    } else if (isOperationsSelected) {
      return FILENAME_MAPPING.DENTAL.OPERATIONS[normalizedMonth] || FILENAME_MAPPING.DEFAULT;
    } else if (isPayrollSelected) {
      return FILENAME_MAPPING.DENTAL.PAYROLL[normalizedMonth] || FILENAME_MAPPING.DEFAULT;
    }
  }

  return FILENAME_MAPPING.DEFAULT;
};

/**
 * Create a chat message object
 * @param {string} content - Message content
 * @param {string} type - Message type (user/ai/system)
 * @param {string} timestamp - Optional timestamp
 * @returns {Object} Message object
 */
export const createMessage = (content, type = MESSAGE_TYPES.USER, timestamp = null) => ({
  id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}-${type}`,
  type,
  content,
  timestamp: timestamp || new Date().toLocaleTimeString(),
});

/**
 * Enhance user message with dashboard summary context
 * @param {string} userMessage - Original user message
 * @param {string} dashboardSummary - Dashboard summary context
 * @returns {string} Enhanced message
 */
export const enhanceMessageWithContext = (userMessage, dashboardSummary) => {
  if (!dashboardSummary) return userMessage;
  
  return `Dashboard Summary Context: ${dashboardSummary}\n\nUser Question: ${userMessage}`;
};

/**
 * Extract answer from API response
 * @param {Object} response - API response object
 * @returns {string} Extracted answer
 */
export const extractAnswerFromResponse = (response) => {
  if (!response) return "No response received";
  
  // Try different possible response structures
  const possiblePaths = [
    response.answer,
    response.output,
    response.response,
    response.message,
    response.content,
    response.data?.plainAnswer,
    response.data?.answer,
    response.data?.output,
    response.data?.response,
    response.data?.message,
    response.data?.content,
  ];
  
  const answer = possiblePaths.find(path => path && typeof path === 'string');
  return answer || "No answer received";
};

/**
 * Format welcome message using the centralized CHAT_MESSAGES.WELCOME template
 * with dynamic content based on selected options
 * @param {string} selectedMonth - Selected month
 * @param {boolean} isFinancialSelected - Financial dashboard selected
 * @param {boolean} isOperationsSelected - Operations dashboard selected
 * @param {boolean} isPayrollSelected - Payroll dashboard selected
 * @param {string} selectedDashboard - Dashboard type (chp/dental)
 * @returns {string} Formatted welcome message
 */
export const formatWelcomeMessage = (
  selectedMonth = 'June',
  isFinancialSelected = true,
  isOperationsSelected = false,
  isPayrollSelected = false,
  selectedDashboard = 'chp'
) => {
  // Determine the selected option
  let selectedOption = '';
  if (isFinancialSelected) {
    selectedOption = 'Financial';
  } else if (isOperationsSelected) {
    selectedOption = 'Operations';
  } else if (isPayrollSelected) {
    selectedOption = 'Payroll';
  }

  // Determine the dashboard type
  const dashboardType = selectedDashboard === 'dental' ? 'Dental' : 'CHP';
  
  // Get current year
  const currentYear = new Date().getFullYear();

  // Use centralized template and replace placeholders
  return CHAT_MESSAGES.WELCOME
    .replace('{selectedOption}', selectedOption)
    .replace('{dashboardType}', dashboardType)
    .replace('{selectedMonth}', selectedMonth)
    .replace('{currentYear}', String(currentYear))
    .replace('{selectedOptionLower}', selectedOption.toLowerCase());
};
