"use client";

import Filters from "@/components/listing/Filters";
import StatsCards from "@/components/listing/StatsCards";
import { useState, useEffect, useMemo } from "react";
import ClientTable from "@/components/listing/ClientTable";
import { LISTING_CONSTANTS } from "@/utils/constants";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchOrganizations,
  getOrganizationById,
} from "@/redux/Thunks/organization.js";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import { ROLE_CONSTANTS } from "@/utils/constants/role";

export default function ListingPage() {
  const [searchTerm, setSearchTerm] = useState(LISTING_CONSTANTS.SEARCH.ALL);
  const [statusFilter, setStatusFilter] = useState(
    LISTING_CONSTANTS.STATUS_FILTER.ALL
  );

  const dispatch = useDispatch();
  const { userRole, user } = useAuthContext();
  const { organizations, currentOrganization, loading, error } = useSelector(
    (state) => state.organizations
  );

  // Convert single organization to array for consistency
  const organizationsList = useMemo(() => {
    if (userRole === ROLE_CONSTANTS.ROLE_TYPES.USER && currentOrganization) {
      return [currentOrganization];
    }
    return organizations || [];
  }, [userRole, currentOrganization, organizations]);

  useEffect(() => {
    if (userRole === ROLE_CONSTANTS.ROLE_TYPES.USER) {
      // For user role, fetch organization by user's organization_id
      if (user?.organization_id) {
        dispatch(getOrganizationById(user.organization_id));
      }
    } else {
      // For other roles (admin), fetch all organizations
      dispatch(fetchOrganizations());
    }
  }, [dispatch, userRole, user?.organization_id]);

  return (
    <div className="min-h-screen max-w-7xl mx-auto px-4 py-6">
      {/* Page Header */}
      <div className="mb-6">
        <Filters
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          addButtonText={LISTING_CONSTANTS.ADD_BUTTON_TEXT}
          addButtonPath={LISTING_CONSTANTS.ADD_BUTTON_PATH}
        />
      </div>

      {/* Stats Cards */}
      <div className="mb-6">
        <StatsCards organizations={organizationsList} loading={loading} />
      </div>

      {/* Client Table */}
      <div className="bg-white rounded-xl shadow-sm">
        <ClientTable
          searchTerm={searchTerm}
          statusFilter={statusFilter}
          organizations={organizationsList}
          loading={loading}
          error={error}
        />
      </div>
    </div>
  );
}
