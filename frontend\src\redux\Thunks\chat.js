import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { CHAT_API_CONFIG, CHAT_DEFAULTS, CHAT_ERRORS } from "../../utils/constants/chat";

// Create optimized axios instance for CFO Insights Service (singleton pattern)
let chatApiClient = null;

const getChatApiClient = () => {
  if (!chatApiClient) {
    chatApiClient = axios.create({
      baseURL: CHAT_API_CONFIG.BASE_URL,
      headers: {
        "Content-Type": "application/json",
      },
      timeout: CHAT_API_CONFIG.TIMEOUT,
    });
  }
  return chatApiClient;
};

/**
 * Start a chat session for a specific document
 * POST /api/chat/start
 */
export const startChatSession = createAsyncThunk(
  "chat/startChatSession",
  async (payload, { rejectWithValue }) => {
    try {
      const { filename } = payload;
      
      // Enhanced validation
      if (!filename || typeof filename !== 'string' || filename.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.FILENAME_REQUIRED);
      }
      
      const apiClient = getChatApiClient();
      const response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.START, {
        filename: filename.trim(),
      });

      // Debug: Log the actual API response

      // Handle both success wrapper and direct response formats
      if (response.data.success) {
        // API returns { success: true, data: { sessionId, filename } }
        const { sessionId, filename: responseFilename } = response.data.data;
        
        if (!sessionId) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_INVALID);
        }
        
        return {
          sessionId,
          filename: responseFilename || filename,
        };
      } else if (response.data.sessionId) {
        // API returns { sessionId, filename } directly
        const { sessionId, filename: responseFilename } = response.data;
        
        if (!sessionId) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_INVALID);
        }
        
        return {
          sessionId,
          filename: responseFilename || filename,
        };
      } else {
        throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
      }
    } catch (error) {
      // Handle different error types
      
      // Enhanced error handling
      if (error.code === 'ECONNABORTED') {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVICE_UNAVAILABLE);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }

      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.START_SESSION_FAILED
      );
    }
  }
);

/**
 * Send a message in an existing chat session
 * POST /api/chat/message
 * Automatically retries by creating a new session if the current one is expired
 */
export const sendChatMessage = createAsyncThunk(
  "chat/sendChatMessage",
  async (payload, { rejectWithValue, dispatch, getState }) => {
    try {
      const { 
        sessionId, 
        message, 
        organization,
        filename // Optional: filename to use for auto-retry if session expires
      } = payload;
      
      // Enhanced validation
      if (!sessionId || typeof sessionId !== 'string' || sessionId.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_REQUIRED);
      }
      if (!message || typeof message !== 'string' || message.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_EMPTY);
      }
      if (message.length > 2000) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG);
      }
      
      const apiClient = getChatApiClient();
      let response;
      
      try {
        response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.MESSAGE, {
          sessionId: sessionId.trim(),
          message: message.trim(),
          organization: organization?.trim(),
        });
      } catch (error) {
        // Check if it's specifically the "Session not found or expired" error with 404 status
        // Only retry for this exact error, not for any other 404 or error
        const errorMessage = error.response?.data?.message || error.response?.data?.error || '';
        const errorMessageLower = errorMessage.toLowerCase();
        const isSessionExpired = 
          error.response?.status === 404 && 
          (errorMessageLower === 'session not found or expired' ||
           errorMessageLower.includes('session not found or expired') || 
           (errorMessageLower.includes('session') && 
            errorMessageLower.includes('not found') && 
            errorMessageLower.includes('expired')));

        // Auto-retry: Create a new session ONLY if it's the specific "Session not found or expired" error with 404
        if (isSessionExpired && filename) {
          try {
            // Start a new session with the filename
            const startResult = await dispatch(startChatSession({ filename })).unwrap();
            
            if (startResult && startResult.sessionId) {
              // Retry message request with new session ID
              response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.MESSAGE, {
                sessionId: startResult.sessionId,
                message: message.trim(),
                organization: organization?.trim(),
              });
              // Continue with normal response handling below
            } else {
              throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
            }
          } catch (retryError) {
            // If retry also fails, throw the original error
            throw error;
          }
        } else {
          // Re-throw the original error if we can't retry
          throw error;
        }
      }

      // Handle both success wrapper and direct response formats
      if (response.data.success) {
        // API returns { success: true, data: { plainAnswer, filename } }
        const { plainAnswer, filename: responseFilename } = response.data.data;
        
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        
        return {
          answer: plainAnswer,
          filename: responseFilename || filename,
        };
      } else if (response.data.plainAnswer) {
        // API returns { plainAnswer, filename } directly
        const { plainAnswer, filename: responseFilename } = response.data;
        
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        
        return {
          answer: plainAnswer,
          filename: responseFilename || filename,
        };
      } else {
        throw new Error(response.data.message || CHAT_ERRORS.SEND_MESSAGE_FAILED);
      }
    } catch (error) {
      // Enhanced error handling
      if (error.code === 'ECONNABORTED') {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SESSION_NOT_FOUND);
      }
      if (error.response?.status === 429) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.RATE_LIMITED);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }
      
      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.SEND_MESSAGE_FAILED
      );
    }
  }
);

/**
 * Send a summary request in an existing chat session (summary mode)
 * POST /api/chat/summary
 * Automatically retries by creating a new session if the current one is expired
 */
export const sendSummaryMessage = createAsyncThunk(
  "chat/sendSummaryMessage",
  async (payload, { rejectWithValue, dispatch, getState }) => {
    try {
      const { 
        sessionId, 
        message, 
        organization,
        filename // Optional: filename to use for auto-retry if session expires
      } = payload;

      // Validation
      if (!sessionId || typeof sessionId !== 'string' || sessionId.trim().length === 0) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.SESSION_ID_REQUIRED);
      }
      if (message && message.length > 2000) {
        throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG);
      }

      const apiClient = getChatApiClient();
      const body = {
        sessionId: sessionId.trim(),
        organization: organization?.trim(),
      };
      if (typeof message === 'string' && message.trim().length > 0) {
        body.message = message.trim();
      }
      
      let response;
      try {
        response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.SUMMARY, body);
      } catch (error) {
        // Check if it's specifically the "Session not found or expired" error with 404 status
        // Only retry for this exact error, not for any other 404 or error
        const errorMessage = error.response?.data?.message || error.response?.data?.error || '';
        const errorMessageLower = errorMessage.toLowerCase();
        const isSessionExpired = 
          error.response?.status === 404 && 
          (errorMessageLower === 'session not found or expired' ||
           errorMessageLower.includes('session not found or expired') || 
           (errorMessageLower.includes('session') && 
            errorMessageLower.includes('not found') && 
            errorMessageLower.includes('expired')));

        // Auto-retry: Create a new session ONLY if it's the specific "Session not found or expired" error with 404
        if (isSessionExpired && filename) {
          try {
            // Start a new session with the filename
            const startResult = await dispatch(startChatSession({ filename })).unwrap();
            
            if (startResult && startResult.sessionId) {
              // Retry summary request with new session ID
              const retryBody = {
                sessionId: startResult.sessionId,
                organization: organization?.trim(),
              };
              if (typeof message === 'string' && message.trim().length > 0) {
                retryBody.message = message.trim();
              }
              
              response = await apiClient.post(CHAT_API_CONFIG.ENDPOINTS.SUMMARY, retryBody);
              // Continue with normal response handling below
            } else {
              throw new Error(CHAT_ERRORS.START_SESSION_FAILED);
            }
          } catch (retryError) {
            // If retry also fails, throw the original error
            throw error;
          }
        } else {
          // Re-throw the original error if we can't retry
          throw error;
        }
      }

      if (response.data.success) {
        const { plainAnswer, filename: responseFilename } = response.data.data;
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        return { answer: plainAnswer, filename: responseFilename || filename };
      } else if (response.data.plainAnswer) {
        const { plainAnswer, filename: responseFilename } = response.data;
        if (!plainAnswer) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.NO_AI_RESPONSE);
        }
        return { answer: plainAnswer, filename: responseFilename || filename };
      } else {
        throw new Error(response.data.message || CHAT_ERRORS.GET_SUMMARY_FAILED);
      }
    } catch (error) {
      if (error.code === 'ECONNABORTED') {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.TIMEOUT);
      }
      if (error.response?.status === 404) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SESSION_NOT_FOUND);
      }
      if (error.response?.status === 429) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.RATE_LIMITED);
      }
      if (error.response?.status >= 500) {
        return rejectWithValue(CHAT_ERRORS.NETWORK_ERRORS.SERVER_ERROR);
      }
      return rejectWithValue(
        error.response?.data?.message || error.message || CHAT_ERRORS.GET_SUMMARY_FAILED
      );
    }
  }
);