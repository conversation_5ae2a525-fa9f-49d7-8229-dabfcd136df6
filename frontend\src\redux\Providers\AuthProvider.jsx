"use client";

import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import {
  login as loginThunk,
  logout as logoutThunk,
  refreshAuth,
  getProfile,
} from "@/redux/Thunks/Authentication";
import { getUserById } from "@/redux/Thunks/userThunks";
import { clearError } from "@/redux/Slice/Authentication";
import tokenStorage from "@/lib/tokenStorage";
import { ROLE_CONSTANTS } from "@/utils/constants";
import { ROLES } from "@/utils/const";

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();
  const router = useRouter();

  const { user, loading, error } = useSelector((state) => state.auth || {});
  const { currentUser: fullUserDetails } = useSelector(
    (state) => state.users || {}
  );

  // Get user data from JWT token if available
  const userFromToken = tokenStorage.getUserDataFromToken();

  // Get user ID from source (token or auth state) before full details are loaded
  const sourceUser = userFromToken || user;
  const userId = sourceUser?.id;

  // Use full user details if available, otherwise use token data or Redux state
  const currentUser = fullUserDetails || userFromToken || user;
  const currentIsAuthenticated = !!(
    tokenStorage.getAccessToken() && currentUser
  );

  // Fetch full user details when authenticated
  useEffect(() => {
    const hasToken = tokenStorage.getAccessToken();
    if (hasToken && userId && !fullUserDetails && !loading) {
      // Fetch full user details using getUserById
      dispatch(getUserById(userId));
    }
  }, [dispatch, userId, fullUserDetails, loading]);

  // Only check profile if we have a token but no user data from token
  useEffect(() => {
    const hasToken = tokenStorage.getAccessToken();
    if (hasToken && !userFromToken && !loading && !fullUserDetails) {
      dispatch(getProfile());
    }
  }, [dispatch, userFromToken, loading, fullUserDetails]);

  const login = useCallback(
    async (credentials) => {
      try {
        const result = await dispatch(loginThunk(credentials)).unwrap();
        // Fetch full user details after successful login
        if (result?.id) {
          dispatch(getUserById(result.id));
        }
        return { success: true, data: result };
      } catch (error) {
        return { success: false, message: error };
      }
    },
    [dispatch]
  );

  const logout = useCallback(async () => {
    try {
      await dispatch(logoutThunk());
      router.push("/login");
    } catch (error) {
      console.error("Logout error:", error);
      // Force logout even if API call fails
      router.push("/login");
    }
  }, [dispatch, router]);

  const refreshToken = useCallback(async () => {
    try {
      await dispatch(refreshAuth()).unwrap();
      // After successful refresh, get updated user data from new token
      const updatedUserFromToken = tokenStorage.getUserDataFromToken();
      // Fetch full user details if user ID is available
      if (updatedUserFromToken?.id) {
        dispatch(getUserById(updatedUserFromToken.id));
      }
      return true;
    } catch (error) {
      console.error("Token refresh failed:", error);
      return false;
    }
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Get current user role
  const userRole = useMemo(() => {
    return currentUser?.role || null;
  }, [currentUser]);

  const isAdmin = useMemo(() => {
    return userRole === ROLES.ADMIN;
  }, [userRole]);

  const authValue = useMemo(
    () => ({
      user: currentUser,
      userRole,
      isAuthenticated: currentIsAuthenticated,
      isAdmin,
      loading,
      error,
      login,
      logout,
      refreshToken,
      clearAuthError,
    }),
    [
      currentUser,
      userRole,
      currentIsAuthenticated,
      isAdmin,
      loading,
      error,
      login,
      logout,
      refreshToken,
      clearAuthError,
    ]
  );

  return (
    <AuthContext.Provider value={authValue}>{children}</AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};

export default AuthProvider;
