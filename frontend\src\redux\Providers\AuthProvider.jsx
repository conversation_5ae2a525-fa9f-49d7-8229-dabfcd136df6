"use client";

import {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useCallback,
} from "react";
import { useSelector, useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import {
  login as loginThunk,
  logout as logoutThunk,
  refreshAuth,
  getProfile,
} from "@/redux/Thunks/Authentication";
import { clearError } from "@/redux/Slice/Authentication";
import tokenStorage from "@/lib/tokenStorage";

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const dispatch = useDispatch();
  const router = useRouter();

  const { user, loading, error, isAuthenticated } = useSelector(
    (state) => state.auth || {}
  );

  // Get user data from JWT token if available
  const userFromToken = tokenStorage.getUserDataFromToken();

  // Use user data from token if available, otherwise use Redux state
  const currentUser = userFromToken || user;
  const currentIsAuthenticated = !!(
    tokenStorage.getAccessToken() && currentUser
  );

  // Only check profile if we have a token but no user data from token
  useEffect(() => {
    const hasToken = tokenStorage.getAccessToken();
    if (hasToken && !userFromToken && !loading) {
      dispatch(getProfile());
    }
  }, [dispatch, userFromToken, loading]);

  const login = useCallback(
    async (credentials) => {
      try {
        const result = await dispatch(loginThunk(credentials)).unwrap();
        return { success: true, data: result };
      } catch (error) {
        return { success: false, message: error };
      }
    },
    [dispatch]
  );

  const logout = useCallback(async () => {
    try {
      await dispatch(logoutThunk());
      router.push("/login");
    } catch (error) {
      console.error("Logout error:", error);
      // Force logout even if API call fails
      router.push("/login");
    }
  }, [dispatch, router]);

  const refreshToken = useCallback(async () => {
    try {
      const result = await dispatch(refreshAuth()).unwrap();
      // After successful refresh, get updated user data from new token
      const updatedUserFromToken = tokenStorage.getUserDataFromToken();
      return true;
    } catch (error) {
      console.error("Token refresh failed:", error);
      return false;
    }
  }, [dispatch]);

  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Get current user role
  const userRole = useMemo(() => {
    return currentUser?.role || null;
  }, [currentUser]);

  const authValue = useMemo(
    () => ({
      user: currentUser,
      userRole,
      isAuthenticated: currentIsAuthenticated,
      loading,
      error,
      login,
      logout,
      refreshToken,
      clearAuthError,
    }),
    [
      currentUser,
      userRole,
      currentIsAuthenticated,
      loading,
      error,
      login,
      logout,
      refreshToken,
      clearAuthError,
    ]
  );

  return (
    <AuthContext.Provider value={authValue}>{children}</AuthContext.Provider>
  );
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
};

export default AuthProvider;
