import { NextResponse } from "next/server";

/**
 * Decode JWT token payload
 * @param {string} token - JWT token
 * @returns {Object|null} Decoded payload or null
 */
function decodeToken(token) {
  if (!token) return null;

  try {
    const parts = token.split(".");
    if (parts.length !== 3) return null;

    const payload = JSON.parse(atob(parts[1]));
    return payload;
  } catch (error) {
    console.error("Error decoding token:", error);
    return null;
  }
}

/**
 * Check if token is expired
 * @param {Object} payload - Decoded token payload
 * @returns {boolean} True if token is expired
 */
function isTokenExpired(payload) {
  if (!payload || !payload.exp) return true;

  const currentTime = Date.now() / 1000;
  return payload.exp < currentTime;
}

/**
 * Get token from request
 * @param {Request} request - Next.js request object
 * @returns {string|null} Token or null
 */
function getTokenFromRequest(request) {
  // Try to get token from Authorization header
  const authHeader = request.headers.get("authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }

  // Try to get token from cookies
  const accessToken = request.cookies.get("accessToken");
  if (accessToken) {
    return accessToken.value;
  }

  return null;
}

/**
 * Check if user is authenticated
 * @param {Request} request - Next.js request object
 * @returns {Object} Authentication status and user data
 */
function checkAuthentication(request) {
  const token = getTokenFromRequest(request);

  if (!token) {
    return { isAuthenticated: false, user: null };
  }

  const payload = decodeToken(token);

  if (!payload || isTokenExpired(payload)) {
    return { isAuthenticated: false, user: null };
  }

  // Extract user data from token
  const user = {
    id: payload.userId,
    email: payload.email,
    role: payload.roles && payload.roles.length > 0 ? payload.roles[0] : "user",
    roles: payload.roles || [],
    organization_id: payload.organization_id,
    schema_name: payload.schema_name,
  };

  return { isAuthenticated: true, user };
}

/**
 * Get redirect URL based on user role
 * @param {Object} user - User object
 * @returns {string} Redirect URL
 */
function getRedirectUrlByRole(user) {
  if (!user) return "/login";

  if (user.role === "admin") {
    return "/listing";
  }

  return "/dashboard";
}

/**
 * Next.js middleware function
 * @param {Request} request - Next.js request object
 * @returns {NextResponse} Next.js response
 */
export function middleware(request) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files, API routes, and Next.js internals
  if (
    pathname.startsWith("/_next") ||
    pathname.startsWith("/api") ||
    pathname.startsWith("/static") ||
    pathname.includes(".") ||
    pathname === "/favicon.ico"
  ) {
    return NextResponse.next();
  }

  const { isAuthenticated, user } = checkAuthentication(request);

  // Public routes that don't require authentication
  const publicRoutes = [
    "/login",
    "/register",
    "/forgot-password",
    "/reset-password",
  ];
  const isPublicRoute = publicRoutes.some((route) =>
    pathname.startsWith(route)
  );

  // Root path handling
  if (pathname === "/") {
    if (isAuthenticated) {
      // Redirect authenticated users to their appropriate dashboard
      const redirectUrl = getRedirectUrlByRole(user);
      return NextResponse.redirect(new URL(redirectUrl, request.url));
    } else {
      // Redirect unauthenticated users to login
      return NextResponse.redirect(new URL("/login", request.url));
    }
  }

  // Handle public routes
  if (isPublicRoute) {
    if (isAuthenticated) {
      // Redirect authenticated users away from auth pages
      const redirectUrl = getRedirectUrlByRole(user);
      return NextResponse.redirect(new URL(redirectUrl, request.url));
    }
    // Allow access to public routes for unauthenticated users
    return NextResponse.next();
  }

  // Protected routes - require authentication
  if (!isAuthenticated) {
    // Store the attempted URL for redirect after login
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("redirect", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Role-based access control for specific routes
  if (pathname.startsWith("/masters") && user.role !== "admin") {
    // Non-admin users trying to access admin routes
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // Add user data to headers for use in components (optional)
  const response = NextResponse.next();
  response.headers.set("x-user-id", user.id);
  response.headers.set("x-user-role", user.role);
  response.headers.set("x-user-email", user.email);

  return response;
}

/**
 * Middleware configuration
 * Define which paths should be processed by this middleware
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\..*$).*)",
  ],
};
