"use client";

import { useState, useRef, useEffect, memo, useCallback, useMemo, lazy, Suspense } from "react";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { ScrollArea } from "../ui/scroll-area";
import {
  Send,
  Bot,
  ArrowLeft,
  Sparkles,
  X,
  HelpCircle,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
} from "lucide-react";
import {
  addMessage,
  setHasWelcomed,
  resetChat,
} from "@/redux/Slice/chat";
import {
  startChatSession,
  sendChatMessage,
  sendSummaryMessage,
} from "@/redux/Thunks/chat";
import { generatePdfFilename, formatWelcomeMessage, createMessage } from "@/utils/chat";
import { MESSAGE_TYPES, CHAT_MESSAGES, CHAT_ERRORS } from "@/utils/constants/chat";
import { formatMessageContent } from "@/utils/formatMessage";
import { useRequestDeduplication } from "@/hooks/useRequestDeduplication";

// Lazy load heavy components for better performance
const DashboardSummaryPopup = lazy(() => import("./CFO-chatbot/DashboardSummaryPopup"));
const Loader = lazy(() => import("../common/Loader"));


const CFOInsightsPage = memo(function CFOInsightsPage({
  onBack,
  selectedMonth,
  selectedMonthKey,
  selectedPage,
  isFinancialSelected,
  isOperationsSelected,
  isPayrollSelected = false,
  selectedDashboard = 'chp',
  organizationName,
  onViewSummary,
}) {
  const router = useRouter();
  const dispatch = useDispatch();
  
  // Redux state - memoized selector to prevent unnecessary re-renders
  const { session, messages, dashboardSummary, isLoading, hasWelcomed } = useSelector(
    (state) => ({
      session: state.chat.session,
      messages: state.chat.messages,
      dashboardSummary: state.chat.dashboardSummary,
      isLoading: state.chat.ui.isLoading,
      hasWelcomed: state.chat.ui.hasWelcomed,
    }),
    (prev, next) =>
      prev.session === next.session &&
      prev.messages === next.messages &&
      prev.dashboardSummary === next.dashboardSummary &&
      prev.isLoading === next.isLoading &&
      prev.hasWelcomed === next.hasWelcomed
  );
  
  // Local state
  const [question, setQuestion] = useState("");
  const [showSidebox, setShowSidebox] = useState(true);
  const [isInitializing, setIsInitializing] = useState(false);
  const [showSummaryPopup, setShowSummaryPopup] = useState(false);
  const [isFetchingSummary, setIsFetchingSummary] = useState(false);
  const scrollAreaRef = useRef(null);
  const scrollViewportRef = useRef(null);
  const welcomeAddedRef = useRef(false);
  const { makeRequest: makeDedupRequest, clearCache: clearDedupCache } = useRequestDeduplication(2000);

  // Sidebar section states
  const [expandedSections, setExpandedSections] = useState({
    gettingStarted: false,
    exampleQuestions: false,
    keyFeatures: false
  });

  // Toggle section expansion - memoized to prevent recreation
  const toggleSection = useCallback((sectionName) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  }, []);

  // Memoize the welcome message content to avoid recalculation
  const welcomeContent = useMemo(() => {
    return formatWelcomeMessage(
      selectedMonth,
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected,
      selectedDashboard
    );
  }, [selectedMonth, isFinancialSelected, isOperationsSelected, isPayrollSelected, selectedDashboard]);



  // Memoize the filename generation - dynamic based on dashboard, month, and section
  const filename = useMemo(() => {
    return generatePdfFilename(
      selectedMonthKey || selectedMonth,
      isFinancialSelected,
      isOperationsSelected,
      isPayrollSelected,
      selectedDashboard
    );
  }, [selectedMonthKey, selectedMonth, isFinancialSelected, isOperationsSelected, isPayrollSelected, selectedDashboard]);

  // Show welcome message instantly on mount - only once
  useEffect(() => {
    // Check if welcome message already exists in messages
    const hasWelcomeMessage = messages.some(msg =>
      msg.content?.includes('Welcome to CFO Dashboard')
    );

    if (!hasWelcomed && !welcomeAddedRef.current && !hasWelcomeMessage) {
      const welcomeMessage = createMessage(welcomeContent, MESSAGE_TYPES.AI);
      dispatch(addMessage(welcomeMessage));
      dispatch(setHasWelcomed(true));
      welcomeAddedRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  // Auto-initialize chat session and get dashboard summary
  useEffect(() => {
    let isMounted = true;
    const initializeChat = async () => {
      try {
        // Only initialize if we don't have an active session
        if (!session.isActive && filename) {
          setIsInitializing(true);

          // Use deduplication to prevent duplicate initialization calls
          await makeDedupRequest(`init-${filename}`, async () => {
            // Start chat session
            const startResult = dispatch(startChatSession({ filename }));
            const startResultData = await startResult;

            if (isMounted && startChatSession.fulfilled.match(startResultData)) {
              const { sessionId } = startResultData.payload;
              // Get dashboard summary via /summary endpoint
              dispatch(sendSummaryMessage({
                sessionId,
                organization: organizationName,
                filename, // Pass filename for auto-retry on session expiration
              }));
            }
          });
        }
      } catch (error) {
        console.error('Failed to initialize chat:', error);
      } finally {
        if (isMounted) {
          setIsInitializing(false);
        }
      }
    };

    initializeChat();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filename, makeDedupRequest]); // Only re-run when filename changes to prevent duplicate calls

  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    if (!question.trim()) return;

    // Input validation
    if (question.length > 2000) {
      const errorMessage = createMessage(
        CHAT_ERRORS.VALIDATION_ERRORS.MESSAGE_TOO_LONG,
        MESSAGE_TYPES.AI
      );
      dispatch(addMessage(errorMessage));
      return;
    }

    // Add user message to chat
    const userMessage = createMessage(question.trim(), MESSAGE_TYPES.USER);
    dispatch(addMessage(userMessage));
    setQuestion("");

    try {
      // If we don't have a chat session, start one first
      if (!session.id) {
        // Use the memoized filename which is already generated based on current selections
        if (!filename) {
          throw new Error(CHAT_ERRORS.VALIDATION_ERRORS.FILENAME_INVALID);
        }
        
        const startResult = await dispatch(startChatSession({ filename }));
        
        if (startChatSession.rejected.match(startResult)) {
          throw new Error(startResult.payload || CHAT_ERRORS.START_SESSION_FAILED);
        }
      }

      // Send user's dynamic question to message API (chat mode)
      const messageResult = await dispatch(sendChatMessage({
        sessionId: session.id,
        message: question.trim(), // User's dynamic question
        organization: organizationName,
        filename, // Pass filename for auto-retry on session expiration
      }));

      if (sendChatMessage.rejected.match(messageResult)) {
        throw new Error(messageResult.payload || CHAT_ERRORS.SEND_MESSAGE_FAILED);
      }
    } catch (error) {
      const errorMessage = createMessage(
        error.message || CHAT_ERRORS.GENERIC_ERROR,
        MESSAGE_TYPES.AI
      );
      dispatch(addMessage(errorMessage));
    }
  }, [question, dispatch, session.id, filename, organizationName]);

  const handleEndChat = useCallback(async () => {
    try {
      // Use the memoized filename which is already generated based on current selections
      // Step 1: Call start API to get session ID
      const startResult = await dispatch(startChatSession({ filename }));
      
      if (startChatSession.fulfilled.match(startResult)) {
        const sessionId = startResult.payload.sessionId;

        // Step 2: Call summary API
        const summaryResult = await dispatch(sendSummaryMessage({
          sessionId,
          organization: organizationName,
          filename, // Pass filename for auto-retry on session expiration
        }));

        if (sendSummaryMessage.fulfilled.match(summaryResult)) {
          // Summary will be automatically stored in Redux state
        }
      }
    } catch (error) {
      // Handle error silently
    }

    // Clear chat history and reset state
    dispatch(resetChat());
    setQuestion("");
    welcomeAddedRef.current = false; // Reset the welcome ref
    
    if (onBack) {
      onBack();
    } else {
      router.push("/dashboard");
    }
  }, [dispatch, filename, onBack, router, organizationName]);

  const toggleSidebox = useCallback(() => {
    setShowSidebox(prev => !prev);
  }, []);

  // Handle View Summary button - fetch/refresh summary with summary flag true
  const handleViewSummary = useCallback(async () => {
    try {
      setIsFetchingSummary(true);
      let currentSessionId = session.id;

      // Ensure we have an active session
      if (!currentSessionId) {
        const startResult = await dispatch(startChatSession({ filename }));
        if (startChatSession.rejected.match(startResult)) {
          // If we have existing summary, still show it
          if (dashboardSummary) {
            setShowSummaryPopup(true);
          }
          setIsFetchingSummary(false);
          return;
        }
        // Get the new session ID
        currentSessionId = startResult.payload?.sessionId;
      }

      // Log session and org before API call
      console.log('sendSummaryMessage API:', {
        sessionId: currentSessionId || session.id,
        organization: organizationName
      });

      // Fetch/refresh dashboard summary using /summary endpoint
      await dispatch(sendSummaryMessage({
        sessionId: currentSessionId || session.id,
        organization: organizationName,
        filename, // Pass filename for auto-retry on session expiration
      }));

      // Open the popup after fetching
      setShowSummaryPopup(true);

      // Call the callback if provided (for external triggers like PDF viewer button)
      if (onViewSummary) {
        onViewSummary();
      }
    } catch (error) {
      console.error('Failed to fetch summary:', error);
      // Still open popup if summary exists in state
      if (dashboardSummary) {
        setShowSummaryPopup(true);
      }
    } finally {
      setIsFetchingSummary(false);
    }
  }, [dispatch, session.id, filename, organizationName, dashboardSummary, onViewSummary]);

  const scrollToBottom = useCallback(() => {
    const el = scrollViewportRef.current;
    if (el) {
      el.scrollTop = el.scrollHeight;
    }
  }, []);

  // Memoized scroll function for better performance
  const scrollToBottomWithDelay = useCallback(() => {
    const el = scrollViewportRef.current;
    if (el) {
        requestAnimationFrame(() => {
        el.scrollTop = el.scrollHeight;
        });
    }
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottomWithDelay();
  }, [messages, scrollToBottomWithDelay]);

  // Scroll to bottom when loading state changes
  useEffect(() => {
    if (!isLoading) {
      scrollToBottomWithDelay();
    }
  }, [isLoading, scrollToBottomWithDelay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Reset welcome ref on unmount
      welcomeAddedRef.current = false;
      // Clear deduplication cache
      clearDedupCache();
    };
  }, [clearDedupCache]);

  // Cache the scroll viewport element once after mount
  useEffect(() => {
    if (!scrollViewportRef.current && scrollAreaRef.current) {
      const el = scrollAreaRef.current.querySelector(
        "[data-radix-scroll-area-viewport]"
      );
      if (el) scrollViewportRef.current = el;
    }
  }, []);

  // Memoize the character count display
  const characterCount = useMemo(() => {
    return CHAT_MESSAGES.UI_TEXT.CHARACTER_COUNT.replace('{current}', question.length).replace('{max}', '2000');
  }, [question.length]);

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="flex flex-col h-full w-full overflow-hidden"
    >
      {/* Fixed Header */}
      <div className="w-full bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white shadow-2xl flex-shrink-0 z-10 relative overflow-hidden">
        {/* Animated background pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/20 via-purple-600/20 to-pink-600/20 animate-pulse"></div>
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            backgroundRepeat: 'repeat'
          }}></div>
        </div>
        <div className="p-3 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {onBack && (
                <Button
                  onClick={onBack}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white/20 transition-all duration-300 hover:scale-110 rounded-full p-2"
                >
                  <ArrowLeft className="w-5 h-5" />
                </Button>
              )}
              <div className="flex items-center gap-4">
                <motion.div
                  animate={{ 
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm"
                >
                  <Sparkles className="w-6 h-6 text-yellow-300" />
                </motion.div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                    CFO Insights Assistant
                  </h1>
                  <p className="text-white/90 text-lg">
                    AI-powered analysis
                  </p>
                  {selectedMonth && (
                    <motion.p 
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-white/80 text-sm mt-2 bg-white/10 px-3 py-1 rounded-full inline-block backdrop-blur-sm"
                    >
                      {isOperationsSelected
                        ? `Operations Dashboard ${organizationName || "CHP"} - ${selectedMonthKey || selectedMonth}`
                        : isFinancialSelected
                        ? `Financial Dashboard ${organizationName || "CHP"} - ${selectedMonthKey || selectedMonth}`
                        : `${selectedMonthKey || selectedMonth} Dashboard ${organizationName || "CHP"}`}
                    </motion.p>
                  )}
                </div>
              </div>
            </div>

            {/* End Chat Button */}
            <Button
              onClick={handleEndChat}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 transition-all duration-300 hover:scale-105 rounded-full px-4 py-2 backdrop-blur-sm border border-white/20"
            >
              <X className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">End Chat</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content Area - Chat with Input Below */}
      <div
        className={`flex-1 flex flex-col gap-3 pr-4 pt-3 pb-3 pl-0 overflow-hidden transition-all duration-300 ${
          showSidebox ? "mr-80" : "mr-0"
        }`}
      >
        <Card className="flex-1 flex flex-col overflow-hidden shadow-lg border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="flex-shrink-0 border-b border-gray-200/50 bg-gradient-to-r from-blue-50 to-indigo-50 p-3">
            <div className="flex items-center justify-between w-full">
              <CardTitle className="flex items-center gap-2 text-lg">
                <div className="w-7 h-7 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent font-bold">
                AI Chat
                </span>
              </CardTitle>
            </div>
          </CardHeader>

          {/* Scrollable Chat Messages Area */}
          <CardContent className="flex-1 p-0 overflow-hidden">
            <ScrollArea ref={scrollAreaRef} className="h-full">
              <div className="p-3 space-y-2 min-h-full">
                <AnimatePresence>
                  {messages.length === 0 ? (
                    <motion.div
                      key="empty-state"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-center text-gray-500 py-8"
                    >
                      <motion.div
                        animate={{
                          scale: [1, 1.1, 1],
                          rotate: [0, 5, -5, 0],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                      >
                        <Bot className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                      </motion.div>
                      {isInitializing ? (
                        <>
                          <p className="text-base font-medium mb-1">
                            Loading Dashboard Summary...
                          </p>
                          <p className="text-xs text-gray-400">
                            Analyzing your financial data
                          </p>
                        </>
                      ) : (
                        <>
                          <p className="text-base font-medium mb-1">
                            Welcome to CFO Insights!
                          </p>
                          <p className="text-xs text-gray-400">
                            {selectedMonth
                              ? `Ask about your ${
                                  isOperationsSelected
                                    ? `Operations Page ${selectedPage}`
                                    : isFinancialSelected
                                    ? `Financial Page ${selectedPage}`
                                    : selectedMonthKey
                                } Dashboard`
                              : CHAT_MESSAGES.UI_TEXT.START_CONVERSATION}
                          </p>
                        </>
                      )}
                    </motion.div>
                  ) : (
                    messages.map((message, index) => (
                      <motion.div
                        key={message.id || `message-${index}`}
                        initial={{ opacity: 0, y: 15, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        transition={{
                          duration: 0.25,
                          delay: Math.min(index * 0.05, 0.3),
                          ease: "easeOut",
                        }}
                        className={`flex gap-2 ${
                          message.type === "user"
                            ? "justify-end"
                            : "justify-start"
                        }`}
                      >
                        <motion.div
                          className={`relative max-w-[80%] rounded-lg px-3 py-2 shadow-sm text-sm ${
                            message.type === "user"
                              ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white ml-8"
                              : "bg-gradient-to-r from-gray-50 to-blue-50 text-gray-800 border border-gray-200/50 mr-8"
                          }`}
                          whileHover={{ scale: 1.01 }}
                          transition={{ duration: 0.15 }}
                        >
                          <div
                            className={`leading-relaxed ${
                              message.type === "user"
                                ? "text-white"
                                : "text-gray-800"
                            } ${message.type === "ai" ? "ai-message" : ""}`}
                            dangerouslySetInnerHTML={{
                              __html: formatMessageContent(message.content),
                            }}
                          />
                          <p
                            className={`text-[10px] opacity-60 mt-1 ${
                              message.type === "user"
                                ? "text-white/60"
                                : "text-gray-500"
                            }`}
                          >
                            {message.timestamp}
                          </p>
                        </motion.div>
                      </motion.div>
                    ))
                  )}

                  {(isLoading || isInitializing) && messages.length > 0 && (
                    <motion.div
                      key="loading-indicator"
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      className="flex gap-2 justify-start"
                    >
                      <motion.div
                        className="relative max-w-[80%] rounded-lg px-3 py-2 shadow-sm bg-gradient-to-r from-gray-50 to-blue-50 text-gray-800 border border-gray-200/50 mr-8"
                        whileHover={{ scale: 1.01 }}
                        transition={{ duration: 0.15 }}
                      >
                        <div className="flex items-center gap-2">
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{
                              duration: 1,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                            className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"
                          />
                          <span className="text-xs text-gray-600 font-medium">
                            {isInitializing ? "Initializing..." : "Thinking..."}
                          </span>
                        </div>
                      </motion.div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </ScrollArea>
          </CardContent>
        </Card>

        {/* Input Area - Below Chat Messages */}
        <Card className="flex-shrink-0 shadow-md border border-gray-200/50 bg-white/95 backdrop-blur-sm">
          <CardContent className="p-2">
            <form onSubmit={handleSubmit} className="flex gap-2 items-end">
              <div className="flex-1 relative">
                <Textarea
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  placeholder={isInitializing ? "Loading..." : CHAT_MESSAGES.PLACEHOLDER}
                  className="min-h-[36px] max-h-[100px] resize-none border border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-100 rounded-lg pr-14 transition-all duration-200 bg-white text-sm py-2 px-3"
                  disabled={isLoading || isInitializing}
                  rows={1}
                  maxLength={2000}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSubmit(e);
                    }
                  }}
                />
                <div className="absolute bottom-1.5 right-2 text-[10px] text-gray-400 bg-gray-50 px-1.5 py-0.5 rounded border border-gray-200">
                  {characterCount}
                </div>
              </div>
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
              <Button
                type="submit"
                  disabled={!question.trim() || isLoading || isInitializing}
                  size="sm"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 px-4 h-[36px] rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium flex-shrink-0"
                >
                  {isInitializing ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear",
                      }}
                      className="w-3 h-3 border-2 border-white border-t-transparent rounded-full"
                    />
                  ) : (
                    <Send className="w-3 h-3" />
                  )}
                </Button>
              </motion.div>
            </form>
          </CardContent>
        </Card>
      </div>

      {/* How to Use CFO Insights - Right Side */}
      <motion.div
        initial={{ x: showSidebox ? 0 : 320 }}
        animate={{ x: showSidebox ? 0 : 320 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className="fixed right-0 top-0 z-20 h-screen w-80 bg-white/95 backdrop-blur-md shadow-2xl border-l border-gray-200/50"
      >
        <div className="h-full flex flex-col">
          {/* Sidebox Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200/50 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                <HelpCircle className="w-4 h-4 text-white" />
              </div>
              <h3 className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                How to Use CFO Insights
              </h3>
            </div>
            <Button
              onClick={toggleSidebox}
              variant="ghost"
              size="sm"
              className="p-2 hover:bg-white/50 rounded-full transition-all duration-300"
            >
              <ChevronRight className="w-4 h-4" />
            </Button>
          </div>

          {/* Sidebox Content */}
          <div className="flex-1 p-6 overflow-y-auto bg-gradient-to-b from-white/50 to-gray-50/50">
            <div className="space-y-6">
              {/* Getting Started */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200/50 overflow-hidden">
                <button
                  onClick={() => toggleSection('gettingStarted')}
                  className="w-full p-4 flex items-center justify-between hover:bg-blue-100/50 transition-colors"
                >
                  <h4 className="font-bold text-gray-800 flex items-center gap-3 text-lg whitespace-nowrap">
                    <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Sparkles className="w-3 h-3 text-white" />
                    </div>
                    <span className="truncate">Getting Started</span>
                  </h4>
                  {expandedSections.gettingStarted ? (
                    <ChevronDown className="w-5 h-5 text-gray-600" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                {expandedSections.gettingStarted && (
                  <div className="px-4 pb-4 text-sm text-gray-700 space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">Ask questions about your financial data</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">Get AI-powered insights and analysis</p>
                    </div>
                  </div>
                )}
              </div>


              {/* Example Questions */}
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200/50 overflow-hidden">
                <button
                  onClick={() => toggleSection('exampleQuestions')}
                  className="w-full p-4 flex items-center justify-between hover:bg-green-100/50 transition-colors"
                >
                  <h4 className="font-bold text-gray-800 flex items-center gap-3 text-lg whitespace-nowrap">
                    <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <Bot className="w-3 h-3 text-white" />
                    </div>
                    <span className="truncate">Example Questions</span>
                  </h4>
                  {expandedSections.exampleQuestions ? (
                    <ChevronDown className="w-5 h-5 text-gray-600" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                {expandedSections.exampleQuestions && (
                  <div className="px-4 pb-4 text-sm text-gray-700 space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">&ldquo;What&apos;s my total revenue for July?&rdquo;</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">&ldquo;Show me expenses by category&rdquo;</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">&ldquo;What&apos;s my profit margin trend?&rdquo;</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">&ldquo;Analyze my cash flow&rdquo;</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Features */}
              <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl border border-orange-200/50 overflow-hidden">
                <button
                  onClick={() => toggleSection('keyFeatures')}
                  className="w-full p-4 flex items-center justify-between hover:bg-orange-100/50 transition-colors"
                >
                  <h4 className="font-bold text-gray-800 flex items-center gap-3 text-lg whitespace-nowrap">
                    <div className="w-6 h-6 bg-gradient-to-r from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <HelpCircle className="w-3 h-3 text-white" />
                    </div>
                    <span className="truncate">Key Features</span>
                  </h4>
                  {expandedSections.keyFeatures ? (
                    <ChevronDown className="w-5 h-5 text-gray-600" />
                  ) : (
                    <ChevronRight className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                {expandedSections.keyFeatures && (
                  <div className="px-4 pb-4 text-sm text-gray-700 space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">Natural language queries</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">Real-time financial analysis</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">Document-based insights</p>
                    </div>
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <p className="font-medium">Professional formatting</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Tips */}
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-4 border border-yellow-200/50">
                <h4 className="flex items-center gap-3 font-bold text-gray-800 mb-4 text-lg">
                  <div className="w-6 h-6 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                    <Sparkles className="w-3 h-3 text-white" />
                  </div>
                  Pro Tips
                </h4>
                <div className="text-sm text-gray-700 space-y-3">
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="font-medium">Be specific with your questions</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="font-medium">Ask for comparisons and trends</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="font-medium">Request visualizations when needed</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                    <p className="font-medium">Use follow-up questions for deeper insights</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Sidebox Toggle Button - Right Side */}
      <motion.div
        initial={{ x: showSidebox ? 0 : 320 }}
        animate={{ x: showSidebox ? 0 : 320 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className="fixed right-80 top-0 z-20"
      >
        <Button
          onClick={toggleSidebox}
          variant="outline"
          size="sm"
          className="rounded-r-none border-r-0 shadow-lg bg-white hover:bg-gray-50"
        >
          <ChevronLeft className="w-4 h-4" />
        </Button>
      </motion.div>

      {/* Custom CSS for better scrollbar and formatted content */}
      <style jsx global>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #cbd5e1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #94a3b8;
        }

        /* Formatted content styling */
        .ai-message h3 {
          color: #1f2937;
          font-size: 1.125rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
          margin-top: 1rem;
        }

        .ai-message h3:first-child {
          margin-top: 0;
        }

        .ai-message h4 {
          color: #374151;
          font-size: 1rem;
          font-weight: 500;
          margin-bottom: 0.5rem;
          margin-top: 0.75rem;
        }

        .ai-message h5 {
          color: #4b5563;
          font-size: 0.875rem;
          font-weight: 500;
          margin-bottom: 0.25rem;
          margin-top: 0.5rem;
        }

        .ai-message ul {
          margin-bottom: 0.75rem;
        }

        .ai-message li {
          margin-bottom: 0.25rem;
        }

        .ai-message strong {
          font-weight: 600;
        }

        .ai-message em {
          font-style: italic;
        }
      `}</style>

      {/* Dashboard Summary Popup - Lazy Loaded */}
      <Suspense fallback={null}>
        <DashboardSummaryPopup
          isOpen={showSummaryPopup}
          onClose={() => setShowSummaryPopup(false)}
          dashboardSummary={dashboardSummary}
          selectedMonth={selectedMonth}
          isFinancialSelected={isFinancialSelected}
          isOperationsSelected={isOperationsSelected}
          isSummaryLoading={isFetchingSummary} // Pass the loading state to the popup
        />
      </Suspense>

      {/* Global Loader for View Summary - Lazy Loaded */}
      <Suspense fallback={null}>
        <Loader show={isFetchingSummary} message="Loading dashboard summary..." />
      </Suspense>
    </motion.div>
  );
});

export default CFOInsightsPage;
