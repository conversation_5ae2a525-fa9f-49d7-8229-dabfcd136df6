"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthContext } from "@/redux/Providers/AuthProvider";
import { Loader } from "@/components/ui/loading";
import tokenStorage from "@/lib/tokenStorage";

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, user, loading } = useAuthContext();
  const [isRedirecting, setIsRedirecting] = useState(false);
  console.log("User role", user?.role);
  useEffect(() => {
    try {
      // Only redirect if we're not already redirecting
      if (!isRedirecting && !loading) {
        setIsRedirecting(true);

        if (!isAuthenticated) {
          router.push("/login");
        } else {
          // Redirect based on user role
          if (user?.role === "admin") {
            router.push("/listing");
          } else {
            router.push("/dashboard");
          }
        }
      }
    } catch (error) {
      console.error("Error in useEffect:", error);
    }
  }, [isAuthenticated, user, loading, router, isRedirecting]);

  // Show loader while checking authentication or redirecting
  if (loading || isRedirecting) {
    return (
      <Loader
        text={loading ? "Checking authentication..." : "Redirecting..."}
      />
    );
  }

  // This should rarely be reached due to the redirects above
  return <Loader text="Loading..." />;
}
